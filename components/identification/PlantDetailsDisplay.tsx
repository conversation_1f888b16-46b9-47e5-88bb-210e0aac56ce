import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import {
  ChevronDown,
  ChevronUp,
  Leaf,
  Sun,
  Droplets,
  Thermometer,
  AlertTriangle,
  MapPin,
  Calendar,
  Flower,
  TreePine,
  Scissors,
  Users,
  Lightbulb,
} from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { Card } from '@/components/ui/Card';
import { Plant } from '@/types/plant';

interface PlantDetailsDisplayProps {
  plant: Plant;
  confidence: number;
  identificationData?: any;
  isDiagnosis?: boolean;
}

export const PlantDetailsDisplay: React.FC<PlantDetailsDisplayProps> = ({
  plant,
  confidence,
  identificationData,
  isDiagnosis = false,
}) => {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});

  const toggleSection = (sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey],
    }));
  };

  const renderExpandableSection = (
    title: string,
    content: React.ReactNode,
    sectionKey: string,
    icon?: React.ReactNode
  ) => (
    <Card style={styles.expandableCard}>
      <TouchableOpacity
        style={styles.expandableHeader}
        onPress={() => toggleSection(sectionKey)}
      >
        {icon && <View style={styles.sectionIcon}>{icon}</View>}
        <Text style={styles.expandableTitle}>{title}</Text>
        {expandedSections[sectionKey] ? (
          <ChevronUp size={20} color={Colors.primary} />
        ) : (
          <ChevronDown size={20} color={Colors.primary} />
        )}
      </TouchableOpacity>
      {expandedSections[sectionKey] && (
        <View style={styles.expandableContent}>
          {content}
        </View>
      )}
    </Card>
  );

  const renderToxicityWarning = () => {
    if (!identificationData?.toxicity || identificationData.toxicity.level === 'none') return null;

    const getWarningColor = () => {
      switch (identificationData.toxicity.level) {
        case 'mild': return Colors.warning;
        case 'moderate': return '#FF8C00';
        case 'severe': return Colors.error;
        default: return Colors.warning;
      }
    };

    return (
      <Card style={[styles.warningCard, { borderColor: getWarningColor() }]}>
        <View style={styles.warningHeader}>
          <AlertTriangle size={20} color={getWarningColor()} />
          <Text style={[styles.warningTitle, { color: getWarningColor() }]}>
            Toxicity Warning
          </Text>
        </View>
        <Text style={styles.warningText}>
          {identificationData.toxicity.warning}
        </Text>
      </Card>
    );
  };

  const renderCareIndicators = () => (
    <View style={styles.careIndicators}>
      <View style={styles.careIndicator}>
        <Sun size={20} color={Colors.primary} />
        <Text style={styles.careIndicatorLabel}>Light</Text>
        <Text style={styles.careIndicatorValue}>
          {plant.careInstructions.light.charAt(0).toUpperCase() + plant.careInstructions.light.slice(1)}
        </Text>
      </View>
      <View style={styles.careIndicator}>
        <Droplets size={20} color={Colors.primary} />
        <Text style={styles.careIndicatorLabel}>Water</Text>
        <Text style={styles.careIndicatorValue}>
          {plant.careInstructions.water.charAt(0).toUpperCase() + plant.careInstructions.water.slice(1)}
        </Text>
      </View>
      <View style={styles.careIndicator}>
        <Thermometer size={20} color={Colors.primary} />
        <Text style={styles.careIndicatorLabel}>Temp</Text>
        <Text style={styles.careIndicatorValue}>
          {plant.careInstructions.temperature.min}°-{plant.careInstructions.temperature.max}°C
        </Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Main Plant Information */}
      <Card style={styles.mainCard}>
        <View style={styles.plantHeader}>
          <Text style={styles.commonName}>{plant.commonName}</Text>
          <Text style={styles.scientificName}>{plant.scientificName}</Text>

          {identificationData?.plantType && (
            <View style={styles.plantTypeContainer}>
              <Leaf size={16} color={Colors.primary} />
              <Text style={styles.plantType}>{identificationData.plantType}</Text>
            </View>
          )}

          <View style={styles.confidenceContainer}>
            <Text style={styles.confidenceLabel}>Confidence: </Text>
            <Text style={styles.confidenceValue}>{Math.round(confidence * 100)}%</Text>
          </View>
        </View>

        {/* Toxicity Warning */}
        {renderToxicityWarning()}

        {/* Basic Plant Info */}
        <Card style={styles.infoCard}>
          <Text style={styles.sectionTitle}>Plant Information</Text>
          
          {identificationData?.nativeRegion && (
            <View style={styles.infoRow}>
              <MapPin size={16} color={Colors.primary} />
              <Text style={styles.infoLabel}>Native Region:</Text>
              <Text style={styles.infoValue}>{identificationData.nativeRegion}</Text>
            </View>
          )}

          {identificationData?.growthHabit && (
            <View style={styles.infoRow}>
              <TreePine size={16} color={Colors.primary} />
              <Text style={styles.infoLabel}>Growth Habit:</Text>
              <Text style={styles.infoValue}>{identificationData.growthHabit}</Text>
            </View>
          )}

          {identificationData?.growthRate && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Growth Rate:</Text>
              <Text style={styles.infoValue}>{identificationData.growthRate}</Text>
            </View>
          )}
        </Card>
      </Card>

      {/* Care Instructions */}
      <Card style={styles.careCard}>
        <Text style={styles.sectionTitle}>Care Instructions</Text>
        {renderCareIndicators()}

        <View style={styles.careDetails}>
          {plant.careInstructions.soil && (
            <View style={styles.careDetailRow}>
              <Text style={styles.careLabel}>Soil:</Text>
              <Text style={styles.careValue}>{plant.careInstructions.soil}</Text>
            </View>
          )}
          {plant.careInstructions.fertilizer && (
            <View style={styles.careDetailRow}>
              <Text style={styles.careLabel}>Fertilizer:</Text>
              <Text style={styles.careValue}>{plant.careInstructions.fertilizer}</Text>
            </View>
          )}
          <View style={styles.careDetailRow}>
            <Text style={styles.careLabel}>Humidity:</Text>
            <Text style={styles.careValue}>
              {plant.careInstructions.humidity.charAt(0).toUpperCase() + plant.careInstructions.humidity.slice(1)}
            </Text>
          </View>
        </View>

        {plant.careInstructions.toxicity && plant.careInstructions.toxicity !== 'none' && (
          <View style={styles.toxicityContainer}>
            <Text style={styles.toxicityLabel}>⚠️ Toxicity: </Text>
            <Text style={styles.toxicityValue}>{plant.careInstructions.toxicity}</Text>
          </View>
        )}
      </Card>

      {/* Plant Description */}
      <Card style={styles.descriptionCard}>
        <Text style={styles.sectionTitle}>About This Plant</Text>
        <Text style={styles.description}>{plant.description}</Text>
      </Card>

      {/* Tags */}
      {plant.tags && plant.tags.length > 0 && (
        <Card style={styles.tagsCard}>
          <Text style={styles.sectionTitle}>Tags</Text>
          <View style={styles.tagsContainer}>
            {plant.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>
        </Card>
      )}

      {/* Enhanced Information Sections */}
      {identificationData && !isDiagnosis && (
        <>
          {/* Plant Characteristics */}
          {renderExpandableSection(
            "Plant Characteristics",
            <View>
              {identificationData.matureSize && (
                <View style={styles.characteristicRow}>
                  <Text style={styles.characteristicLabel}>Mature Size:</Text>
                  <Text style={styles.characteristicValue}>
                    {identificationData.matureSize.height} tall × {identificationData.matureSize.width} wide
                  </Text>
                </View>
              )}
              
              {identificationData.bloomTime && identificationData.bloomTime !== 'Non-flowering' && (
                <View style={styles.characteristicRow}>
                  <Text style={styles.characteristicLabel}>Blooms:</Text>
                  <Text style={styles.characteristicValue}>
                    {identificationData.bloomTime}
                    {identificationData.flowerColor && identificationData.flowerColor.length > 0 &&
                      ` (${identificationData.flowerColor.join(', ')})`}
                  </Text>
                </View>
              )}

              {identificationData.foliageType && (
                <View style={styles.characteristicRow}>
                  <Text style={styles.characteristicLabel}>Foliage:</Text>
                  <Text style={styles.characteristicValue}>{identificationData.foliageType}</Text>
                </View>
              )}

              {identificationData.hardiness && (
                <View style={styles.characteristicRow}>
                  <Text style={styles.characteristicLabel}>Hardiness:</Text>
                  <Text style={styles.characteristicValue}>
                    Zones {identificationData.hardiness.zones}
                    {identificationData.hardiness.minTemperature &&
                      ` (min ${identificationData.hardiness.minTemperature})`}
                  </Text>
                </View>
              )}
            </View>,
            "characteristics",
            <Leaf size={20} color={Colors.primary} />
          )}

          {/* Seasonal Care */}
          {identificationData.additionalInfo?.seasonalCare && renderExpandableSection(
            "Seasonal Care",
            <Text style={styles.expandableText}>{identificationData.additionalInfo.seasonalCare}</Text>,
            "seasonal",
            <Calendar size={20} color={Colors.primary} />
          )}

          {/* Propagation */}
          {identificationData.additionalInfo?.propagation && renderExpandableSection(
            "Propagation",
            <Text style={styles.expandableText}>{identificationData.additionalInfo.propagation}</Text>,
            "propagation",
            <Scissors size={20} color={Colors.primary} />
          )}

          {/* Uses & Benefits */}
          {identificationData.additionalInfo?.uses && identificationData.additionalInfo.uses.length > 0 && renderExpandableSection(
            "Uses & Benefits",
            <View>
              {identificationData.additionalInfo.uses.map((use: string, index: number) => (
                <Text key={index} style={styles.expandableText}>• {use}</Text>
              ))}
            </View>,
            "uses",
            <Lightbulb size={20} color={Colors.primary} />
          )}

          {/* Fun Facts */}
          {identificationData.additionalInfo?.funFacts && renderExpandableSection(
            "Fun Facts",
            <Text style={styles.expandableText}>{identificationData.additionalInfo.funFacts}</Text>,
            "facts",
            <Lightbulb size={20} color={Colors.primary} />
          )}

          {/* Companion Plants */}
          {identificationData.additionalInfo?.companionPlants && identificationData.additionalInfo.companionPlants.length > 0 && renderExpandableSection(
            "Companion Plants",
            <View>
              {identificationData.additionalInfo.companionPlants.map((plant: string, index: number) => (
                <Text key={index} style={styles.expandableText}>• {plant}</Text>
              ))}
            </View>,
            "companions",
            <Users size={20} color={Colors.primary} />
          )}

          {/* Pests & Diseases */}
          {identificationData.additionalInfo?.pestsAndDiseases && renderExpandableSection(
            "Pests & Diseases",
            <Text style={styles.expandableText}>{identificationData.additionalInfo.pestsAndDiseases}</Text>,
            "pests",
            <AlertTriangle size={20} color={Colors.warning} />
          )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  mainCard: {
    marginBottom: 16,
    borderRadius: 16,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  plantHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  commonName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 4,
  },
  scientificName: {
    fontSize: 18,
    fontStyle: 'italic',
    color: Colors.textMuted,
    textAlign: 'center',
    marginBottom: 8,
  },
  plantTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 8,
  },
  plantType: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
    marginLeft: 4,
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  confidenceLabel: {
    fontSize: 16,
    color: Colors.textMuted,
  },
  confidenceValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  warningCard: {
    backgroundColor: '#FFF8E1',
    borderWidth: 1,
    borderLeftWidth: 4,
    marginBottom: 16,
  },
  warningHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  warningTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  warningText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
  infoCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textMuted,
    marginLeft: 8,
    marginRight: 8,
  },
  infoValue: {
    fontSize: 14,
    color: Colors.text,
    flex: 1,
  },
  careCard: {
    marginBottom: 16,
  },
  careIndicators: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  careIndicator: {
    alignItems: 'center',
    flex: 1,
  },
  careIndicatorLabel: {
    fontSize: 12,
    color: Colors.textMuted,
    marginTop: 4,
    marginBottom: 2,
  },
  careIndicatorValue: {
    fontSize: 12,
    color: Colors.text,
    fontWeight: '500',
    textAlign: 'center',
  },
  careDetails: {
    marginTop: 12,
  },
  careDetailRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  careLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textMuted,
    marginRight: 8,
    minWidth: 80,
  },
  careValue: {
    fontSize: 14,
    color: Colors.text,
    flex: 1,
  },
  toxicityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.errorLight,
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
  },
  toxicityLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.error,
    marginRight: 4,
  },
  toxicityValue: {
    fontSize: 14,
    color: Colors.error,
    textTransform: 'capitalize',
  },
  descriptionCard: {
    marginBottom: 16,
  },
  description: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
  tagsCard: {
    marginBottom: 16,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tagText: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '500',
  },
  expandableCard: {
    marginBottom: 12,
  },
  expandableHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  sectionIcon: {
    marginRight: 8,
  },
  expandableTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    flex: 1,
  },
  expandableContent: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    marginTop: 8,
  },
  expandableText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
    marginBottom: 4,
  },
  characteristicRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  characteristicLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textMuted,
    marginRight: 8,
    minWidth: 100,
  },
  characteristicValue: {
    fontSize: 14,
    color: Colors.text,
    flex: 1,
  },
});
